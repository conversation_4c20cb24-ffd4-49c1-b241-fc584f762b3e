/* SellerRequests Component Styles */
.seller-requests-container {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
}

.seller-requests-container .requests-table {
  width: 100%;
  font-size: var(--basefont);
  background-color: var(--white);

  border-radius: var(--border-radius-large);
  overflow: hidden;
  overflow-x: auto;
}

.seller-requests-container .requests-table th {
  padding: 12px 10px;
  text-align: left;
  vertical-align: middle;
}

.seller-requests-container .requests-table td {
  padding: 12px 10px;
  text-align: left;
  border-top: 1px solid var(--light-gray);
  vertical-align: middle;
}

.seller-requests-container .video-doc {
  display: flex;
  align-items: center;
  gap: 10px;
}

.seller-requests-container .video-doc img {
  width: 55px;
  height: 55px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.seller-requests-container .video-doc span {
  font-size: var(--smallfont);
  font-weight: 500;
  color: var(--text-color);
}

/* Action icons */
.seller-requests-container .action-icons {
  display: flex;
  gap: var(--smallfont);
  align-items: center;
  justify-content: center;
}

.seller-requests-container .eye-icon,
.seller-requests-container .comment-icon {
  font-size: var(--heading6);
  color: black;
  cursor: pointer;
  transition: all 0.2s ease;

  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

.seller-requests-container .action-icon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: scale(1.02);
}
.seller-requests-container .eye-icon:hover,
.seller-requests-container .comment-icon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: scale(1.02);
}

/* Enhanced Requests Styles */
.seller-requests-container .requests-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--heading4);
  gap: var(--heading5);
}

.seller-requests-container .requests-stats {
  display: flex;
  gap: var(--heading5);
}

.seller-requests-container .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--basefont);
  background: var(--primary-light-color);
  border-radius: var(--border-radius);
  min-width: 100px;
}

.seller-requests-container .stat-icon {
  font-size: var(--heading4);
  color: var(--primary-color);
  margin-bottom: var(--extrasmallfont);
}

.seller-requests-container .stat-count {
  font-size: var(--heading4);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: var(--extrasmallfont);
}

.seller-requests-container .stat-label {
  font-size: var(--smallfont);
  color: var(--gray);
  text-align: center;
}

.seller-requests-container .requests-filters {
  display: flex;
  gap: var(--basefont);
  align-items: center;
}

.seller-requests-container .filter-group,
.seller-requests-container .search-group {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.seller-requests-container .filter-select,
.seller-requests-container .search-input {
  padding: var(--border-radius-medium) var(--extrasmallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background: var(--white);
  transition: border-color 0.2s ease;
}

.seller-requests-container .filter-select:focus,
.seller-requests-container .search-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.seller-requests-container .search-input {
  min-width: 200px;
}

.seller-requests-container .requests-table-container {
  background: var(--white);
  border-radius: var(--border-radius);
  overflow: hidden;
  border: 1px solid var(--light-gray);
}

.seller-requests-container .request-details {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.seller-requests-container .request-title {
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: var(--extrasmallfont);
}

.seller-requests-container .request-meta {
  display: flex;
  gap: var(--border-radius-medium);
}

.seller-requests-container .content-type,
.seller-requests-container .sport {
  font-size: var(--smallfont);
  padding: var(--extrasmallfont) var(--border-radius-medium);
  background: var(--light-gray);
  border-radius: var(--border-radius);
  color: var(--gray);
}

.seller-requests-container .buyer-info {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.seller-requests-container .buyer-name {
  font-weight: 500;
  color: var(--secondary-color);
}

.seller-requests-container .buyer-email {
  font-size: var(--smallfont);
  color: var(--gray);
}

.seller-requests-container .budget-info {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
}

.seller-requests-container .budget-amount {
  font-weight: 600;
  color: var(--success-color);
  font-size: var(--mediumfont);
}

.seller-requests-container .status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  padding: var(--extrasmallfont) var(--border-radius-medium);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  text-transform: capitalize;
}

.seller-requests-container .status-orange {
  background: var(--warning-light-color);
  color: var(--warning-color);
}

.seller-requests-container .status-green {
  background: var(--success-light-color);
  color: var(--success-color);
}

.seller-requests-container .status-red {
  background: var(--error-light-color);
  color: var(--error-color);
}

.seller-requests-container .status-blue {
  background: var(--info-light-color);
  color: var(--info-color);
}

.seller-requests-container .status-purple {
  background: #f3e8ff;
  color: #7c3aed;
}

.seller-requests-container .status-gray {
  background: var(--light-gray);
  color: var(--gray);
}

.seller-requests-container .action-buttons {
  display: flex;
  gap: var(--extrasmallfont);
}

.seller-requests-container .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--basefont);
}

.seller-requests-container .view-btn {
  background: var(--info-light-color);
  color: var(--info-color);
}

.seller-requests-container .view-btn:hover {
  background: var(--info-color);
  color: var(--white);
}

.seller-requests-container .respond-btn {
  background: var(--success-light-color);
  color: var(--success-color);
}

.seller-requests-container .respond-btn:hover {
  background: var(--success-color);
  color: var(--white);
}

.seller-requests-container .payment-btn {
  background: var(--warning-light-color);
  color: var(--warning-color);
}

.seller-requests-container .payment-btn:hover {
  background: var(--warning-color);
  color: var(--white);
}

.seller-requests-container .empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--heading2);
  text-align: center;
}

.seller-requests-container .empty-icon {
  font-size: var(--heading1);
  color: var(--light-gray);
  margin-bottom: var(--heading5);
}

.seller-requests-container .empty-state h3 {
  margin: 0 0 var(--extrasmallfont) 0;
  color: var(--secondary-color);
  font-size: var(--heading5);
}

.seller-requests-container .empty-state p {
  margin: 0;
  color: var(--gray);
  font-size: var(--basefont);
  max-width: 400px;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .seller-requests-container .requests-header {
    flex-direction: column;
    align-items: stretch;
  }

  .seller-requests-container .requests-filters {
    justify-content: space-between;
  }
}

@media (max-width: 768px) {
  .seller-requests-container {
    padding: var(--basefont);
  }

  .seller-requests-container .requests-stats {
    flex-wrap: wrap;
    gap: var(--extrasmallfont);
  }

  .seller-requests-container .stat-item {
    min-width: 80px;
    padding: var(--extrasmallfont);
  }

  .seller-requests-container .requests-filters {
    flex-direction: column;
    gap: var(--extrasmallfont);
  }

  .seller-requests-container .search-input {
    min-width: auto;
  }

  .seller-requests-container .requests-table {
    font-size: var(--smallfont);
  }

  .seller-requests-container .requests-table th,
  .seller-requests-container .requests-table td {
    padding: var(--border-radius-medium) var(--extrasmallfont);
  }

  .seller-requests-container .action-buttons {
    flex-direction: column;
    gap: var(--extrasmallfont);
  }

  .seller-requests-container .action-btn {
    width: 28px;
    height: 28px;
    font-size: var(--smallfont);
  }
}
