.BuyerDownloads {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Table Styles - Following BuyerAccountDashboard pattern */
.BuyerDownloads .table {
  width: 100%;
  border-collapse: collapse;
  font-size: var(--smallfont);
  background-color: var(--white);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  overflow-x: auto;
}

.BuyerDownloads .table th,
.BuyerDownloads .table td {
  padding: 12px 10px;
  text-align: left;
  border-bottom: 1px solid var(--light-gray);
  vertical-align: middle;
}

.BuyerDownloads .table th {
  background-color: var(--bg-gray);
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--extrasmallfont);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.BuyerDownloads .table tr:last-child td {
  border-bottom: none;
}

/* Remove old grid-based table-row/table-header/table-cell styles */
.content-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.BuyerDownloads .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerDownloads .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerDownloads .content-info {
  display: flex;
  flex-direction: column;
  text-align: left; /* Keep text left-aligned within the centered container */
}

.BuyerDownloads .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerDownloads .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerDownloads .content-type {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
  margin-top: 2px;
}

.BuyerDownloads .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerDownloads .status-badge.downloaded {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerDownloads .status-badge.pending {
  background-color: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

/* Action buttons styling */
.BuyerDownloads .action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  justify-content: center;
}

 .action-btn {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.BuyerDownloads .download-btn {
  background-color: var(--second-primary-color);
  color: white;
}

.BuyerDownloads .download-btn:hover:not(:disabled) {
  background-color: var(--second-primary-color);
  transform: translateY(-1px);
}

.BuyerDownloads .download-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

 .view-btn {
  background-color: transparent;
  color: rgb(0, 0, 0) !important;
  font-size: var(--heading6);
  
}
.view-btn:hover {
  transform: scale(1.02);
}

/* Spinner for loading state */
.BuyerDownloads .spinner {
  width: 14px;
  height: 14px;
  border: 2px solid #ffffff;
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
.BuyerDownloads .table-cells {
  font-size: var(--smallfont);
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Tab Navigation */
.content-tabs {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 2px solid var(--light-gray);
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  color: var(--gray);
  font-size: var(--basefont);
  font-weight: 500;
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  color: var(--primary-color);
  background-color: var(--light-gray);
}

.tab-btn.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
  background-color: var(--light-gray);
}

/* Custom Content Grid */
.custom-content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.custom-content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.custom-content-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.content-thumbnail {
  height: 180px;
  overflow: hidden;
  background: var(--light-gray);
}

.content-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.content-info {
  padding: 1.5rem;
}

.content-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
  line-height: 1.4;
}

.request-title {
  font-size: 0.9rem;
  color: var(--gray);
  margin-bottom: 1rem;
  font-style: italic;
}

.content-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.85rem;
}

.content-type {
  background: var(--primary-color);
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-weight: 500;
}

.completion-date {
  color: var(--gray);
}

.download-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.download-btn:hover {
  background: var(--primary-dark);
}

.BuyerDownloads__empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 30px 20px;
  text-align: center;
  background: var(--light-background);
  border-radius: var(--border-radius-large);
  border: 2px dashed var(--light-gray);
}
.BuyerDownloads__empty h3 {
  color: var(--text-color);
  font-size: var(--heading5);
  margin: 0 0 12px 0;
  font-weight: 600;
}
.BuyerDownloads__empty p {
  font-size: var(--basefont);
}

/* Responsive styles */
@media (max-width: 992px) {
  .BuyerDownloads .table-header,
  .BuyerDownloads .table-row {
    grid-template-columns: 0.5fr 1fr 2fr 1.5fr 1fr 1fr 1fr;
  }

  .BuyerDownloads .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerDownloads .table {
    overflow-x: auto;
  }

  .BuyerDownloads .table-header,
  .BuyerDownloads .table-row {
    min-width: 700px;
  }
}

.BuyerDownloads__pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.BuyerDownloads .no-data-message {
  text-align: center;
  padding: 2rem;
  color: #666;
  font-size: 1.1rem;
}
