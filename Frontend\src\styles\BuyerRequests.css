.BuyerRequests {
  display: flex;
  flex-direction: column;
  width: 100%;
}

/* Table Styles - Following BuyerDownloads pattern */
.BuyerRequests .table {
  width: 100%;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.BuyerRequests .table-header {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1.5fr 1fr 0.5fr;
  background-color: var(--bg-gray);
  padding: var(--smallfont) var(--basefont);
  font-weight: 600;
  color: var(--secondary-color);
  border-bottom: 1px solid var(--light-gray);
}

.BuyerRequests .table-row {
  display: grid;
  grid-template-columns: 0.5fr 1fr 3fr 1.5fr 1.5fr 1fr 0.5fr;
  padding: var(--smallfont) var(--basefont);
  border-bottom: 1px solid var(--light-gray);
  align-items: center;
}

.BuyerRequests .table-row:last-child {
  border-bottom: none;
}

.BuyerRequests .table-cell {
  padding: 0 var(--extrasmallfont);
  font-size: var(--smallfont);
  text-align: center; /* Center all content as requested */
}

.BuyerRequests .content-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);

}

.BuyerRequests .content-image {
  width: 50px;
  height: 50px;
  border-radius: var(--border-radius);
  overflow: hidden;
  flex-shrink: 0;
}

.BuyerRequests .content-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.BuyerRequests .content-info {
  display: flex;
  flex-direction: column;
  text-align: left; /* Keep text left-aligned within the centered container */
}

.BuyerRequests .content-title {
  font-weight: 500;
  color: var(--text-color);
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 200px;
}

.BuyerRequests .content-coach {
  font-size: var(--extrasmallfont);
  color: var(--dark-gray);
}

.BuyerRequests .status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: var(--extrasmallfont);
  font-weight: 500;
  text-align: center;
}

.BuyerRequests .status-badge.pending {
  background-color: rgba(243, 156, 18, 0.1);
  color: #f39c12;
}

.BuyerRequests .status-badge.approved {
  background-color: rgba(52, 152, 219, 0.1);
  color: #3498db;
}

.BuyerRequests .status-badge.completed {
  background-color: rgba(46, 204, 113, 0.1);
  color: #2ecc71;
}

.BuyerRequests .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--dark-gray);
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
  font-size: var(--basefont);
}

.BuyerRequests .action-btn:hover {
  color: var(--btn-color);
}

.BuyerRequests__empty {
  text-align: center;
  padding: var(--heading4);
  color: var(--dark-gray);
}

.BuyerRequests__empty p {
  font-size: var(--basefont);
}

/* Responsive styles */
@media (max-width: 992px) {
  .BuyerRequests .table-header,
  .BuyerRequests .table-row {
    grid-template-columns: 0.5fr 1fr 2fr 1.5fr 1.5fr 1fr 0.5fr;
  }

  .BuyerRequests .content-title {
    max-width: 150px;
  }
}

@media (max-width: 768px) {
  .BuyerRequests .table {
    overflow-x: auto;
  }

  .BuyerRequests .table-header,
  .BuyerRequests .table-row {
    min-width: 700px;
  }
}

/* Enhanced Requests Styles */
.BuyerRequests .requests-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--heading5);
  gap: var(--basefont);
}

.BuyerRequests .filter-group,
.BuyerRequests .search-group {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.BuyerRequests .filter-select,
.BuyerRequests .search-input {
  padding: var(--border-radius-medium) var(--extrasmallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
  background: var(--white);
  transition: border-color 0.2s ease;
}

.BuyerRequests .filter-select:focus,
.BuyerRequests .search-input:focus {
  outline: none;
  border-color: var(--primary-color);
}

.BuyerRequests .search-input {
  min-width: 200px;
}

.BuyerRequests .requests-stats {
  display: flex;
  gap: var(--heading5);
  margin-bottom: var(--heading5);
}

.BuyerRequests .stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--basefont);
  background: var(--primary-light-color);
  border-radius: var(--border-radius);
  min-width: 100px;
}

.BuyerRequests .stat-count {
  font-size: var(--heading4);
  font-weight: 700;
  color: var(--secondary-color);
  margin-bottom: var(--extrasmallfont);
}

.BuyerRequests .stat-label {
  font-size: var(--smallfont);
  color: var(--gray);
  text-align: center;
}

.BuyerRequests .request-details {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.BuyerRequests .request-title {
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: var(--extrasmallfont);
}

.BuyerRequests .request-meta {
  display: flex;
  gap: var(--border-radius-medium);
}

.BuyerRequests .content-type,
.BuyerRequests .sport {
  font-size: var(--smallfont);
  padding: var(--extrasmallfont) var(--border-radius-medium);
  background: var(--light-gray);
  border-radius: var(--border-radius);
  color: var(--gray);
}

.BuyerRequests .request-description {
  font-size: var(--smallfont);
  color: var(--gray);
  line-height: 1.4;
}

.BuyerRequests .seller-info {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.BuyerRequests .seller-name {
  font-weight: 500;
  color: var(--secondary-color);
}

.BuyerRequests .seller-email {
  font-size: var(--smallfont);
  color: var(--gray);
}

.BuyerRequests .budget-info {
  display: flex;
  flex-direction: column;
  gap: var(--extrasmallfont);
}

.BuyerRequests .budget-amount {
  font-weight: 600;
  color: var(--success-color);
  font-size: var(--mediumfont);
}

.BuyerRequests .seller-price {
  font-size: var(--smallfont);
  color: var(--gray);
}

.BuyerRequests .status-orange {
  background: var(--warning-light-color);
  color: var(--warning-color);
}

.BuyerRequests .status-green {
  background: var(--success-light-color);
  color: var(--success-color);
}

.BuyerRequests .status-red {
  background: var(--error-light-color);
  color: var(--error-color);
}

.BuyerRequests .status-blue {
  background: var(--info-light-color);
  color: var(--info-color);
}

.BuyerRequests .status-purple {
  background: #f3e8ff;
  color: #7c3aed;
}

.BuyerRequests .status-gray {
  background: var(--light-gray);
  color: var(--gray);
}

.BuyerRequests .action-buttons {
  display: flex;
  gap: var(--extrasmallfont);
}

.BuyerRequests .action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: var(--basefont);
}

.BuyerRequests .view-btn {
  background: var(--info-light-color);
  color: var(--info-color);
}

.BuyerRequests .view-btn:hover {
  background: var(--info-color);
  color: var(--white);
}

.BuyerRequests .payment-btn {
  background: var(--warning-light-color);
  color: var(--warning-color);
}

.BuyerRequests .payment-btn:hover {
  background: var(--warning-color);
  color: var(--white);
}

.BuyerRequests .empty-icon {
  font-size: var(--heading1);
  color: var(--light-gray);
  margin-bottom: var(--heading5);
}

@media (max-width: 1024px) {
  .BuyerRequests .requests-filters {
    flex-direction: column;
    align-items: stretch;
  }
}

@media (max-width: 768px) {
  .BuyerRequests .requests-stats {
    flex-wrap: wrap;
    gap: var(--extrasmallfont);
  }

  .BuyerRequests .stat-item {
    min-width: 80px;
    padding: var(--extrasmallfont);
  }

  .BuyerRequests .search-input {
    min-width: auto;
  }

  .BuyerRequests .action-buttons {
    flex-direction: column;
    gap: var(--extrasmallfont);
  }

  .BuyerRequests .action-btn {
    width: 28px;
    height: 28px;
    font-size: var(--smallfont);
  }
}
